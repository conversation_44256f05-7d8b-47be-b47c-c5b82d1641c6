1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.zhongmi"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         Required to query activities that can process text, see:
12         https://developer.android.com/training/package-visibility and
13         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
14
15         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
16    -->
17    <queries>
17-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:39:5-44:15
18        <intent>
18-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:40:9-43:18
19            <action android:name="android.intent.action.PROCESS_TEXT" />
19-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:41:13-72
19-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:41:21-70
20
21            <data android:mimeType="text/plain" />
21-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:42:13-50
21-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:42:19-48
22        </intent>
23    </queries>
24
25    <permission
25-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
26        android:name="com.example.zhongmi.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.example.zhongmi.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
29-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
30
31    <application
32        android:name="android.app.Application"
32-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:4:9-42
33        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
33-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
34        android:extractNativeLibs="true"
35        android:icon="@mipmap/ic_launcher"
35-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:5:9-43
36        android:label="zhongmi" >
36-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:3:9-32
37        <activity
37-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:6:9-27:20
38            android:name="com.example.zhongmi.MainActivity"
38-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:7:13-41
39            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
39-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:12:13-163
40            android:exported="true"
40-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:8:13-36
41            android:hardwareAccelerated="true"
41-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:13:13-47
42            android:launchMode="singleTop"
42-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:9:13-43
43            android:taskAffinity=""
43-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:10:13-36
44            android:theme="@style/LaunchTheme"
44-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:11:13-47
45            android:windowSoftInputMode="adjustResize" >
45-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:14:13-55
46
47            <!--
48                 Specifies an Android theme to apply to this Activity as soon as
49                 the Android process has started. This theme is visible to the user
50                 while the Flutter UI initializes. After that, this theme continues
51                 to determine the Window background behind the Flutter UI.
52            -->
53            <meta-data
53-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:19:13-22:17
54                android:name="io.flutter.embedding.android.NormalTheme"
54-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:20:15-70
55                android:resource="@style/NormalTheme" />
55-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:21:15-52
56
57            <intent-filter>
57-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:23:13-26:29
58                <action android:name="android.intent.action.MAIN" />
58-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:24:17-68
58-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:24:25-66
59
60                <category android:name="android.intent.category.LAUNCHER" />
60-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:25:17-76
60-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:25:27-74
61            </intent-filter>
62        </activity>
63        <!--
64             Don't delete the meta-data below.
65             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
66        -->
67        <meta-data
67-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:30:9-32:33
68            android:name="flutterEmbedding"
68-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:31:13-44
69            android:value="2" />
69-->/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/AndroidManifest.xml:32:13-30
70
71        <provider
71-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:9:9-17:20
72            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
72-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:10:13-82
73            android:authorities="com.example.zhongmi.flutter.image_provider"
73-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:11:13-74
74            android:exported="false"
74-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:12:13-37
75            android:grantUriPermissions="true" >
75-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:13:13-47
76            <meta-data
76-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:14:13-16:75
77                android:name="android.support.FILE_PROVIDER_PATHS"
77-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:15:17-67
78                android:resource="@xml/flutter_image_picker_file_paths" />
78-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:16:17-72
79        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
80        <service
80-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:19:9-31:19
81            android:name="com.google.android.gms.metadata.ModuleDependencies"
81-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:20:13-78
82            android:enabled="false"
82-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:21:13-36
83            android:exported="false" >
83-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:22:13-37
84            <intent-filter>
84-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:24:13-26:29
85                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
85-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:25:17-94
85-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:25:25-91
86            </intent-filter>
87
88            <meta-data
88-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:28:13-30:36
89                android:name="photopicker_activity:0:required"
89-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:29:17-63
90                android:value="" />
90-->[:image_picker_android] /Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:30:17-33
91        </service>
92
93        <uses-library
93-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
94            android:name="androidx.window.extensions"
94-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
95            android:required="false" />
95-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
96        <uses-library
96-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
97            android:name="androidx.window.sidecar"
97-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
98            android:required="false" />
98-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
99
100        <provider
100-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
101            android:name="androidx.startup.InitializationProvider"
101-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
102            android:authorities="com.example.zhongmi.androidx-startup"
102-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
103            android:exported="false" >
103-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
104            <meta-data
104-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
105                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
105-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
106                android:value="androidx.startup" />
106-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
107            <meta-data
107-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:29:13-31:52
108                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
108-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:30:17-85
109                android:value="androidx.startup" />
109-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:31:17-49
110        </provider>
111
112        <receiver
112-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:34:9-52:20
113            android:name="androidx.profileinstaller.ProfileInstallReceiver"
113-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:35:13-76
114            android:directBootAware="false"
114-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:36:13-44
115            android:enabled="true"
115-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:37:13-35
116            android:exported="true"
116-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:38:13-36
117            android:permission="android.permission.DUMP" >
117-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:39:13-57
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:40:13-42:29
119                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
119-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:41:17-91
119-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:41:25-88
120            </intent-filter>
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:43:13-45:29
122                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
122-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:44:17-85
122-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:44:25-82
123            </intent-filter>
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:46:13-48:29
125                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
125-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:47:17-88
125-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:47:25-85
126            </intent-filter>
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:49:13-51:29
128                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
128-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:50:17-95
128-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:50:25-92
129            </intent-filter>
130        </receiver>
131    </application>
132
133</manifest>
