<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/flutter_plugin_android_lifecycle/intermediates/library_assets/release/packageReleaseAssets/out"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/image_picker_android/intermediates/library_assets/release/packageReleaseAssets/out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/main/assets"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app/src/release/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/app/intermediates/shader_assets/release/compileReleaseShaders/out"/></dataSet></merger>