<lint-module
    format="1"
    dir="/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/android/app"
    name=":app"
    type="APP"
    maven="android:app:unspecified"
    agpVersion="8.7.3"
    buildFolder="/Users/<USER>/Project/newzhongmi/flutterproject/zhongmi/build/app"
    bootClassPath="/Users/<USER>/Library/Android/sdk/platforms/android-35/android.jar:/Users/<USER>/Library/Android/sdk/build-tools/34.0.0/core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
