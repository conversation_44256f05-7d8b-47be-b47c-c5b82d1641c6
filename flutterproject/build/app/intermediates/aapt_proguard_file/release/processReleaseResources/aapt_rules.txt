-keep class android.app.Application { <init>(); }
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class com.example.zhongmi.MainActivity { <init>(); }
-keep class com.google.android.gms.metadata.ModuleDependencies { <init>(); }
-keep class io.flutter.plugins.imagepicker.ImagePickerFileProvider { <init>(); }
